import json
from typing import List, Dict, Any, Optional
from enum import Enum
from pydantic import BaseModel, Field, validator

from datetime import datetime

from app.schemas.agent import AgentCapabilitiesInDB, AgentInDB, CategoryEnum
from app.schemas.mcp import EnvCredentialStatus, EnvKey, MCPInDB, MCPUrl, McpComponentCategory
from app.schemas.workflow import WorkflowInDB


class MarketplaceItemSortEnum(str, Enum):
    NEWEST = "NEWEST"
    OLDEST = "OLDEST"
    MOST_POPULAR = "MOST_POPULAR"
    HIGHEST_RATED = "HIGHEST_RATED"


class PaginationMetadata(BaseModel):
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool
    next_page: Optional[int] = None
    prev_page: Optional[int] = None


class MarketplaceAgentResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    use_count: Optional[int] = None
    visibility: str = "PUBLIC"
    workflow_ids: Optional[List[str]] = None
    mcp_server_ids: Optional[List[str]] = None
    agent_topic_type: Optional[str] = None
    is_a2a: Optional[bool] = None
    is_customizable: Optional[bool] = None
    capabilities: Optional[AgentCapabilitiesInDB] = Field(default=None, alias="agent_capabilities")
    example_prompts: Optional[List[str]] = None
    is_added: Optional[bool] = False
    category: Optional[CategoryEnum] = None
    # Additional fields from MarketplaceAgent proto
    agent_category: Optional[str] = None
    system_message: Optional[str] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    model_api_key: Optional[str] = None
    subscriptions: Optional[str] = None
    tone: Optional[str] = None
    files: Optional[List[str]] = None
    urls: Optional[List[str]] = None
    status: Optional[str] = None
    capabilities_id: Optional[str] = None
    source_workflow_id: Optional[str] = None
    source_agent_id: Optional[str] = None
    version: Optional[str] = None

    class Config:
        from_attributes = True


class AgentWithMCPsInDB(MarketplaceAgentResponse):
    mcps: Optional[List[MCPInDB]] = Field(default_factory=list)
    workflows: Optional[List[WorkflowInDB]] = Field(default_factory=list)


class MarketplaceWorkflowResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    workflow_url: Optional[str] = None  # Stores the workflow schema
    builder_url: Optional[str] = None  # Stores the builder schema
    start_nodes: Optional[List[Dict[str, Any]]] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    use_count: Optional[int] = 0
    execution_count: Optional[int] = 0
    average_rating: Optional[float] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    version: Optional[str] = "1.0.0"
    status: Optional[str] = "ACTIVE"
    visibility: str = "PUBLIC"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    workflow_definition: Optional[Dict[str, Any]] = None
    workflow_steps: Optional[List[Dict[str, Any]]] = None
    is_added: Optional[bool] = False
    available_nodes: Optional[List[Dict[str, Any]]] = None
    source_workflow_id: Optional[str] = None  # ID of the original workflow this was created from

    class Config:
        from_attributes = True


class MarketplaceMCPResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    urls: Optional[List[Dict[str, str]]] = None
    git_url: Optional[str] = None
    mcp_tools_config: Optional[dict] = None
    category: Optional[str] = None
    component_category:Optional[str] = None
    
    @validator("mcp_tools_config", pre=True)
    def validate_mcp_tools_config(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    @validator("urls", pre=True)
    def validate_urls(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v
    class Config:
        from_attributes = True


class PaginatedMarketplaceAgentResponse(BaseModel):
    data: List[MarketplaceAgentResponse]
    metadata: PaginationMetadata


class PaginatedMarketplaceWorkflowResponse(BaseModel):
    data: List[MarketplaceWorkflowResponse]
    metadata: PaginationMetadata


class PaginatedMarketplaceMCPResponse(BaseModel):
    data: List[MarketplaceMCPResponse]
    metadata: PaginationMetadata


class MarketplaceItemTypeEnum(str, Enum):
    AGENT = "AGENT"
    WORKFLOW = "WORKFLOW"
    MCP = "MCP"


class MarketplaceItemResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    item_type: MarketplaceItemTypeEnum
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    # Agent-specific fields
    avatar: Optional[str] = None
    department: Optional[str] = None
    # Workflow-specific fields
    version: Optional[str] = None
    # MCP-specific fields
    sse_url: Optional[str] = None

    class Config:
        from_attributes = True


class CombinedMarketplaceResponse(BaseModel):
    data: List[MarketplaceItemResponse]
    metadata: PaginationMetadata


# Detailed Agent Response
class MarketplaceAgentDetail(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    avatar: Optional[str] = None
    department: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    system_message: Optional[str] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    knowledge_base_ids: Optional[List[str]] = None
    workflow_ids: Optional[List[str]] = None
    mcp_server_ids: Optional[List[str]] = None
    category: Optional[CategoryEnum] = None
    # Additional fields from MarketplaceAgent proto
    agent_category: Optional[str] = None
    model_api_key: Optional[str] = None
    agent_topic_type: Optional[str] = None
    subscriptions: Optional[str] = None
    tone: Optional[str] = None
    files: Optional[List[str]] = None
    urls: Optional[List[str]] = None
    use_count: Optional[int] = None
    status: Optional[str] = None
    is_a2a: Optional[bool] = None
    is_customizable: Optional[bool] = None
    capabilities_id: Optional[str] = None
    example_prompts: Optional[List[str]] = None
    capabilities: Optional[AgentCapabilitiesInDB] = Field(default=None, alias="agent_capabilities")
    is_added: Optional[bool] = False
    source_workflow_id: Optional[str] = None
    source_agent_id: Optional[str] = None
    version: Optional[str] = None

    class Config:
        from_attributes = True


class MarketplaceAgentDetailResponse(BaseModel):
    success: bool
    message: str
    agent: AgentWithMCPsInDB


# Detailed Workflow Response
class MarketplaceWorkflowDetail(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    workflow_url: Optional[str] = None  # Stores the workflow schema
    builder_url: Optional[str] = None  # Stores the builder schema
    start_nodes: Optional[List[Dict[str, Any]]] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    use_count: Optional[int] = 0
    execution_count: Optional[int] = 0
    average_rating: Optional[float] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    version: Optional[str] = "1.0.0"
    status: Optional[str] = "ACTIVE"
    visibility: str = "PUBLIC"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    workflow_definition: Optional[Dict[str, Any]] = None
    workflow_steps: Optional[List[Dict[str, Any]]] = None
    is_added: Optional[bool] = False
    available_nodes: Optional[List[Dict[str, Any]]] = None
    source_workflow_id: Optional[str] = None  # ID of the original workflow this was created from

    class Config:
        from_attributes = True


class MarketplaceWorkflowDetailResponse(BaseModel):
    success: bool
    message: str
    workflow: MarketplaceWorkflowDetail


# Detailed MCP Response
class MarketplaceMCPDetail(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    owner_id: Optional[str] = None
    owner_name: Optional[str] = None
    average_rating: Optional[float] = None
    visibility: str = "PUBLIC"
    urls: Optional[List[Dict[str, str]]] = None
    git_url: Optional[str] = None
    api_documentation: Optional[str] = None
    capabilities: Optional[List[str]] = None
    mcp_tools_config: Optional[dict] = None
    department: Optional[str] = None
    is_added: Optional[bool] = False
    component_category:Optional[str] = None
    env_keys: Optional[List[EnvKey]] = None
    component_category: Optional[McpComponentCategory] = None
    env_credential_status:Optional[EnvCredentialStatus] = None
        
    @validator("mcp_tools_config", pre=True)
    def validate_mcp_tools_config(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v
    
    @validator("urls", pre=True)
    def validate_urls(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v
    class Config:
        from_attributes = True


class MarketplaceMCPDetailResponse(BaseModel):
    success: bool
    message: str
    mcp: MarketplaceMCPDetail


# Rate Marketplace Item Request/Response
class RateMarketplaceItemRequest(BaseModel):
    item_id: str
    rating: float = Field(..., ge=1.0, le=5.0, description="Rating value between 1.0 and 5.0")
    item_type: MarketplaceItemTypeEnum


class RateMarketplaceItemResponse(BaseModel):
    success: bool
    message: str
    item_id: str
    item_type: MarketplaceItemTypeEnum
    rating: float
    average_rating: Optional[float] = None


# Use Marketplace Item Request/Response
class UseMarketplaceItemRequest(BaseModel):
    item_id: str
    item_type: MarketplaceItemTypeEnum


class UseMarketplaceItemResponse(BaseModel):
    success: bool
    message: str
    item_id: str
    item_type: MarketplaceItemTypeEnum
    use_count: int
