from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request
from fastapi.security import OAuth2PasswordRequestForm
from app.services.user_service import UserServiceClient
from app.core.security import get_current_user
from app.core.auth_guard import role_required
from app.schemas.user import (
    GetAllUsersRequest,
    LoginRequestData,
    ProfileUpdateResponse,
    ResetPassword,
    ResetPasswordResponse,
    UpdatePassword,
    UserBase,
    UserCreate,
    UserInfoResponse,
    UserProfileUpdateRequest,
    UserUpdate,
    UserResponse,
    TokenResponse,
    EmailOTP,
    StripeCustomerIdUpdate,
    StripeCustomerIdResponse,
    StripeCustomerIdFetchResponse,
)
from app.core.config import settings
from fastapi.responses import RedirectResponse, JSONResponse
from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error


# Create separate routers for auth and user operations
auth_router = APIRouter(prefix="/auth", tags=["auth"])
user_router = APIRouter(prefix="/users", tags=["users"])
user_service = UserServiceClient()


@auth_router.post(
    "/register",
    summary="Register a new user",
    description="""
    This endpoint allows a user to register with an email, password, and full name.

    - If the email is already registered and verified, an error is returned.
    - If email verification is pending, the user is notified.
    - A verification email with an OTP is sent upon successful registration.
    """,
    responses={
        200: {
            "description": "User registered successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "An email has been sent for verification",
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid email format"}}},
        },
        409: {
            "description": "Email already registered",
            "content": {"application/json": {"example": {"detail": "Email already registered"}}},
        },
        412: {
            "description": "Email verification pending",
            "content": {"application/json": {"example": {"detail": "Email verification pending"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.post("/register", response_model=TokenResponse)
async def register(user_data: UserCreate):
    try:
        response = await user_service.register(
            email=user_data.email, password=user_data.password, full_name=user_data.full_name
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        return JSONResponse(content={"success": response.success, "message": response.message})
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@auth_router.post(
    "/verify-email-otp",
    summary="Verify Email OTP",
    description="""
    This endpoint verifies the OTP sent to the user's email and activates the account.

    - If the OTP is valid, the email is marked as verified, and a welcome email is sent.
    - If the OTP is expired or incorrect, an error is returned.
    - If the email is already verified, an error is returned.
    """,
    responses={
        200: {
            "description": "Email verified successfully",
            "content": {
                "application/json": {
                    "example": {"success": True, "message": "Email verified successfully"}
                }
            },
        },
        400: {
            "description": "Invalid OTP",
            "content": {"application/json": {"example": {"detail": "OTP is incorrect"}}},
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}},
        },
        409: {
            "description": "Email already verified",
            "content": {"application/json": {"example": {"detail": "Email already verified"}}},
        },
        412: {
            "description": "OTP expired",
            "content": {"application/json": {"example": {"detail": "OTP is expired"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.post("/verify-email-otp", response_model=TokenResponse)
async def verify_email_otp(otp_token: EmailOTP):
    try:
        response = await user_service.verify_email_otp(otp_token.token)

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        return JSONResponse(content={"success": response.success, "message": response.message})
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@auth_router.post(
    "/login",
    summary="User Login",
    description="""
    This endpoint allows a user to log in using their email and password.

    - If the user does not exist, a 404 error is returned.
    - If the credentials are incorrect, a 401 error is returned.
    - If the account is inactive, a 412 error is returned and a verification email is resent.
    - On success, an access token and a refresh token are returned.
    """,
    responses={
        200: {
            "description": "Login successful",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Login successful",
                        "access_token": "your_access_token",
                        "refresh_token": "your_refresh_token",
                        "token_type": "bearer",
                    }
                }
            },
        },
        401: {
            "description": "Invalid credentials",
            "content": {"application/json": {"example": {"detail": "Invalid credentials"}}},
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User does not exist"}}},
        },
        412: {
            "description": "Account inactive",
            "content": {
                "application/json": {
                    "example": {"detail": "Account inactive. Verification email resent."}
                }
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.post("/login", response_model=TokenResponse)
async def login(form_data: LoginRequestData):
    try:
        response = await user_service.login(
            email=form_data.email,
            password=form_data.password,
            fcm_token=form_data.fcm_token
        )
        if not response.success:
            raise HTTPException(status_code=401, detail=response.message)
        return JSONResponse(
            content={
                "success": True,
                "message": response.message,
                "access_token": response.accessToken,
                "refresh_token": response.refreshToken,
                "token_type": "bearer",
                "accessTokenAge": response.accessTokenAge,
                "refreshTokenAge": response.refreshTokenAge,
            }
        )
    except Exception as e:
        print(f"[EXCEPTION] {e}")
        error_details = parse_error(str(e))
        print(error_details["message"])
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@auth_router.get(
    "/google-login",
    summary="Initiate Google OAuth Login",
    description="""
    This endpoint initiates the Google OAuth flow by redirecting the user to Google's authorization page.

    - The user will be prompted to log in with their Google account and grant permissions.
    - On success, the user is redirected to the specified `redirect_uri` with an authorization code.
    """,
    responses={
        302: {
            "description": "Redirect to Google OAuth authorization page",
            "headers": {
                "Location": {
                    "description": "URL to Google's OAuth authorization page",
                    "schema": {"type": "string"},
                }
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.get("/google-login")
async def google_auth():
    """
    Initiates the Google OAuth flow by redirecting to Google's authorization page
    """
    try:
        # Google OAuth authorization URL
        auth_url = "https://accounts.google.com/o/oauth2/auth"

        # Parameters for the authorization request
        params = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "response_type": "code",
            "scope": "email profile",
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "access_type": "offline",
            "prompt": "consent",
        }

        # Construct the URL with parameters
        url = auth_url + "?" + "&".join([f"{key}={params[key]}" for key in params])

        # Redirect to Google's authorization page
        return RedirectResponse(url)

    except Exception as e:
        raise HTTPException(status_code=500, details="Internal server error")


@auth_router.get(
    "/google-callback",
    summary="Handle Google OAuth Callback",
    description="""
    This endpoint handles the callback from Google OAuth after the user authorizes the application.

    - It exchanges the authorization code for access and refresh tokens.
    - It retrieves user information from Google and logs the user in or registers them.
    - On success, it returns a JSON response with tokens and user details.
    """,
    responses={
        200: {
            "description": "Google login successful",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Login successful",
                        "access_token": "your_access_token",
                        "refresh_token": "your_refresh_token",
                        "user": {
                            "email": "user email",
                            "fullName": "user full name",
                        },
                    }
                }
            },
        },
        401: {
            "description": "Error signing in with Google",
            "content": {
                "application/json": {"example": {"detail": "Error signing in with Google"}}
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.get("/google-callback")
async def google_callback(code: str, request: Request):
    """
    Handles the callback from Google OAuth and redirects to frontend with token
    """
    try:
        # Call the existing service to process the auth code
        auth_response = await user_service.google_oauth_login(code)

        if not auth_response.success:
            raise HTTPException(status_code=401, details="Error singing in with Google")

        access_token = str(auth_response.accessToken)

        redirect = RedirectResponse(url=f"{settings.FRONTEND_AUTH_URL}/")

        if settings.ENV == 'main' or settings.ENV == 'dev':
            redirect.set_cookie(
                key="accessToken",
                value=access_token,
                secure=True,
                domain=settings.COOKIE_DOMAIN,
                samesite='Lax',
                max_age=60 * 60 * 24  # 1 day
            )
            refresh_token = str(auth_response.refreshToken)
            redirect.set_cookie(
                key="refreshToken",
                value=refresh_token,
                secure=True,
                domain=settings.COOKIE_DOMAIN,
                samesite='Lax',
                max_age=7 * 24 * 60 * 60  # 7 days
            )
            redirect.set_cookie(
                key="accessTokenAge",
                value=str(auth_response.accessTokenAge),
                secure=True,
                domain=settings.COOKIE_DOMAIN,
                samesite='Lax',
                max_age=60 * 60 * 24  # 1 day
            )
            redirect.set_cookie(
                key="refreshTokenAge",
                value=str(auth_response.refreshTokenAge),
                secure=True,
                domain=settings.COOKIE_DOMAIN,
                samesite='Lax',
                max_age=7 * 24 * 60 * 60  # 7 days
            )
        else:
            redirect.set_cookie(
                key="accessToken",
                value=access_token,
                domain='localhost',
                samesite="lax",
                max_age=60 * 60 * 60  # 1 day
            )
            refresh_token = str(auth_response.refreshToken)
            redirect.set_cookie(
                key="refreshToken",
                value=refresh_token,
                domain='localhost',
                max_age=7 * 24 * 60 * 60  # 7 days
            )
            redirect.set_cookie(
                key="accessTokenAge",
                value=str(auth_response.accessTokenAge),
                domain='localhost',
                max_age=60 * 60 * 60  # 1 day
            )
            redirect.set_cookie(
                key="refreshTokenAge",
                value=str(auth_response.refreshTokenAge),
                domain='localhost',
                max_age=7 * 24 * 60 * 60  # 7 days
            )
        return redirect

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@auth_router.post(
    "/access-token",
    response_model=TokenResponse,
    summary="Generate New Access Token",
    description="""
    This endpoint generates a new access token using a valid refresh token.

    - The refresh token is validated against the stored token in Redis and decoded to verify the user.
    - On success, a new access token is returned along with its expiration time.
    - If the refresh token is invalid, expired, or the user is not found, an appropriate error is returned.
    """,
    responses={
        200: {
            "description": "Access token generated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": "true/false",
                        "access_token": "user's access-token",
                        "token_type": "bearer",
                        "tokenExpireAt": "timestamp",
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid or expired refresh token",
            "content": {
                "application/json": {"example": {"detail": "Invalid or expired refresh token"}}
            },
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.post("/access-token", response_model=TokenResponse)
async def access_token(refresh_token: str):
    try:
        response = await user_service.access_token(refresh_token)
        return JSONResponse(
            content={
                "success": response.success,
                "access_token": response.accessToken,
                "token_type": "bearer",
                "tokenExpireAt": response.tokenExpireAt,
                "accessTokenAge": response.accessTokenAge,
            }
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@auth_router.post(
    "/reset-password-otp",
    response_model="",
    summary="Generate Reset Password OTP",
    description="""
    This endpoint generates a one-time password (OTP) for resetting a user's password and sends it via email.

    - If the email is registered, an OTP is generated and emailed to the user for password reset.
    - If the email is not registered, an error is returned.
    - The OTP is stored temporarily and linked to the user's account for verification.
    """,
    responses={
        200: {
            "description": "OTP generated and email sent successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "An email has been sent to your email for password reset",
                    }
                }
            },
        },
        404: {
            "description": "Email not registered",
            "content": {"application/json": {"example": {"detail": "Email not registered"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.post("/reset-password-otp", response_model=UserResponse)
async def reset_password_otp(email: str):
    try:
        response = await user_service.generate_reset_password_otp(email)
        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)
        return JSONResponse(
            content={
                "success": response.success,
                "message": response.message,
            }
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@auth_router.post(
    "/update-password",
    response_model="",
    summary="Update User Password",
    description="""
    This endpoint updates a user's password using a valid OTP token provided during a password reset flow.

    - The token must be a valid OTP token previously generated for password reset.
    - The new password and confirmation password must match and differ from the current password.
    - On success, the password is updated, and the OTP token is invalidated.
    - Errors are returned if the token is invalid, passwords don’t match, or the user is not found.
    """,
    responses={
        200: {
            "description": "Password updated successfully",
            "content": {
                "application/json": {
                    "example": {"success": True, "message": "Password reset successfully"}
                }
            },
        },
        400: {
            "description": "Invalid input (e.g., passwords don’t match or same as old password)",
            "content": {
                "application/json": {
                    "example": {"detail": "Confirm password and password do not match"}
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid or expired OTP token",
            "content": {
                "application/json": {"example": {"detail": "Invalid or expired OTP token"}}
            },
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}},
        },
        412: {
            "description": "OTP is expired",
            "content": {"application/json": {"example": {"detail": "OTP is expired"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@auth_router.post("/update-password", response_model=TokenResponse)
async def update_password(updatePassword: UpdatePassword):
    try:
        response = await user_service.update_password(
            token=updatePassword.token,
            new_password=updatePassword.new_password,
            confirm_new_password=updatePassword.confirm_new_password,
        )
        if not response.success:
            raise HTTPException(status_code=401, detail=response.message)
        return JSONResponse(
            content={
                "success": response.success,
                "message": response.message,
            }
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.get(
    "/me",
    response_model="",
    summary="Get Current User Information",
    description="""
    This endpoint retrieves information about the currently authenticated user.

    - Requires a valid JWT Bearer token in the `Authorization` header (e.g., `Bearer <token>`).
    - The token must be decoded using the JWT secret and validated against an active session in Redis.
    - The user must have the 'user' role to access this endpoint, enforced by role-based access control.
    - Returns the user's email and full name if the user exists and authentication succeeds.
    - Errors are returned if the token is invalid, session is missing, permissions are insufficient, or the user is not found.
    """,
    responses={
        200: {
            "description": "User information retrieved successfully",
            "content": {
                "application/json": {
                    "example": {"email": "<EMAIL>", "fullName": "John Doe", "company": "Company Name", "department": "department Name", "jobRole": "jobRole"}
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid, expired, or missing JWT token, or no active session in Redis",
            "content": {"application/json": {"example": {"detail": "Unauthorized"}}},
        },
        403: {
            "description": "Forbidden - Missing JWT Bearer token or insufficient role permissions (requires 'user' role)",
            "content": {"application/json": {"example": {"detail": "JWT Bearer is missing"}}},
            "headers": {
                "WWW-Authenticate": {
                    "description": "Bearer token required",
                    "schema": {"type": "string", "example": "Bearer"},
                }
            },
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@user_router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(role_required(["user"]))):
    try:
        response = await user_service.get_user(current_user["user_id"])
        print(f"[DEBUG] User response: {response}")
        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)
        return JSONResponse(
            content={
                "id": response.user.userId,
                "email": response.user.email,
                "fullName": response.user.fullName,
                "company": response.user.company,
                "department": response.user.department,
                "jobRole": response.user.jobRole,
                "phoneNumber": response.user.phoneNumber,
                "profileImage": response.user.profileImage,
                "createdAt": response.user.createdAt,
                "updatedAt": response.user.updatedAt,
                "isFirstLogin": response.user.isFirstLogin,
                "github_access_token": response.user.githubAccessToken,
            }
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.get(
    "/list-users",
    response_model="",
    summary="Retrieve a List of Users",
    description="""
    This endpoint fetches a paginated list of users with optional sorting, filtering, and search functionality.

    **Authorization:**
    - Requires a valid JWT Bearer token (`Authorization: Bearer <token>`).
    - The token must be validated against an active session stored in Redis.
    - Only users with the `admin` role can access this endpoint.

    **Request Parameters:**
    - `page` (int, required) → The page number (must be a positive integer).
    - `page_size` (int, required) → Number of users per page (must be a positive integer).
    - `sort_by` (string, optional) → Field to sort by (e.g., "email", "fullName").
    - `sort_order` (string, optional) → Sorting order, either `"asc"` or `"desc"`.
    - `is_email_verified` (bool, optional) → Filter by email verification status.
    - `role` (string, optional) → Filter users by role (`"user"` or `"admin"`).
    - `is_active` (bool, optional) → Filter by active/inactive status.
    - `search` (string, optional) → Search term for matching emails or full names.

    **Response Structure:**
    - Returns a list of users with their email, full name, and role.
    - Includes pagination, sorting, and filter metadata.
    - Returns appropriate error responses for authentication, permission issues, or internal errors.
    """,
    responses={
        200: {
            "description": "Users retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Users retrieved successfully",
                        "users": [
                            {"email": "<EMAIL>", "fullName": "John Doe", "role": "user", "company": "Company Name", "department": "department Name", "jobRole": "jobRole"},
                            {
                                "email": "<EMAIL>",
                                "fullName": "Jane Smith",
                                "role": "admin",
                                "company": "Company Name",
                                "department": "department Name",
                                "jobRole": "jobRole"
                            },
                        ],
                        "pagination": {
                            "currentPage": 1,
                            "totalPages": 5,
                            "totalItems": 50,
                            "pageSize": 10,
                        },
                        "sorting": {"sortBy": "email", "sortOrder": "asc"},
                        "searchFilter": {"appliedFilters": "role=user", "searchTerm": "john"},
                    }
                }
            },
        },
        400: {
            "description": "Invalid request parameters",
            "content": {
                "application/json": {
                    "example": {"detail": "Invalid argument: page must be greater than 0"}
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid, expired, or missing JWT token",
            "content": {"application/json": {"example": {"detail": "Unauthorized"}}},
        },
        403: {
            "description": "Forbidden - Insufficient permissions (admin role required)",
            "content": {
                "application/json": {"example": {"detail": "Access denied, admin role required"}}
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
@user_router.get("/list-users", response_model=UserResponse)
async def get_all_users(
    allUserRequest: GetAllUsersRequest = Depends(),
    current_user: dict = Depends(role_required(["admin"])),
):
    try:
        response = await user_service.get_all_users(allUserRequest)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        users = [
            {"email": user.email, "fullName": user.fullName, "role": user.role, "company": user.company, "department": user.department, "jobRole": user.jobRole, "profileImage": user.profileImage}
            for user in response.users
        ]

        result = {
            "success": response.success,
            "message": response.message,
            "users": users,
            "pagination": (
                {
                    "currentPage": response.pagination.currentPage,
                    "totalPages": response.pagination.totalPages,
                    "totalItems": response.pagination.totalItems,
                    "pageSize": response.pagination.pageSize,
                }
                if response.pagination
                else None
            ),
            "sorting": (
                {
                    "sortBy": response.sorting.sortBy,
                    "sortOrder": response.sorting.sortOrder,
                }
                if response.sorting
                else None
            ),
            "searchFilter": (
                {
                    "appliedFilters": response.searchFilter.appliedFilters,
                    "searchTerm": response.searchFilter.searchTerm,
                }
                if response.searchFilter
                else None
            ),
        }

        return JSONResponse(content=result)

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_data: UserUpdate, current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.update_user(
            user_id=current_user["user_id"],
            full_name=user_data.full_name,
            phone_number=user_data.phone_number,
            profile_image=user_data.profile_image
        )
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        return JSONResponse(
            content={
                "email": response.user.email,
                "fullName": response.user.fullName,
                "company": response.user.company,
                "department": response.user.department,
                "jobRole": response.user.jobRole,
                "phoneNumber": response.user.phoneNumber,
                "profileImage": response.user.profileImage,
                "createdAt": response.user.createdAt,
                "updatedAt": response.user.updatedAt,

            }
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.post(
    "/reset-password",
    response_model=ResetPasswordResponse,
    summary="Reset User Password",
    description="""
    This endpoint allows an authenticated user to reset their password by providing their current password and a new password.

    - The current password must be valid for the user.
    - The new password and confirmation password must match and differ from the current password.
    - On success, the password is updated.
    - Errors are returned if the current password is incorrect, passwords don't match, or the user is not found.
    """,
    responses={
        200: {
            "description": "Password changed successfully",
            "content": {
                "application/json": {
                    "example": {"success": True, "message": "Password changed successfully"}
                }
            },
        },
        400: {
            "description": "Invalid input (e.g., passwords don't match or same as old password)",
            "content": {
                "application/json": {
                    "example": {"detail": "New password and confirm password do not match"}
                }
            },
        },
        401: {
            "description": "Unauthorized - Current password is incorrect",
            "content": {
                "application/json": {"example": {"detail": "Current password is incorrect"}}
            },
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def reset_password(
    reset_password_data: ResetPassword, current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.reset_password(
            user_id=current_user["user_id"],
            current_password=reset_password_data.current_password,
            new_password=reset_password_data.new_password,
            confirm_new_password=reset_password_data.confirm_new_password,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return JSONResponse(
            content={
                "success": response.success,
                "message": response.message,
            }
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.delete("/me")
async def delete_current_user(current_user: dict = Depends(role_required(["user"]))):
    try:
        response = await user_service.delete_user(current_user["user_id"])
        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)
        return {"message": "User deleted successfully"}
    except Exception as e:
        print(f"[EXCEPTION] {e}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# Use PUT for updating existing resource (user profile)
@user_router.put(
    "/profile-details",
    response_model=ProfileUpdateResponse,
    summary="Update User Profile Details",
    description="Updates the current authenticated user's company, department, and job role.",
    responses={
        200: {"description": "Profile updated successfully"},
        500: {"description": "Internal Server Error"},
    }
)
async def update_current_user_profile(
    profile_data: UserProfileUpdateRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.update_user_profile_details(
            user_id=current_user["user_id"],
            company=profile_data.company,
            department=profile_data.department,
            job_role=profile_data.job_role
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        # Map gRPC UserInfo to Pydantic UserInfoResponse
        updated_user_info = None
        if response.user:
             updated_user_info = UserInfoResponse(
                 userId=response.user.userId,
                 email=response.user.email,
                 fullName=response.user.fullName,
                 createdAt=response.user.createdAt,
                 updatedAt=response.user.updatedAt,
                 role=response.user.role,
                 company=response.user.company or None,
                 department=response.user.department or None,
                 jobRole=response.user.jobRole or None
             )

        return ProfileUpdateResponse(
            success=response.success,
            message=response.message,
            user=updated_user_info
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.put(
    "/stripe-customer-id",
    response_model=StripeCustomerIdResponse,
    summary="Update Stripe Customer ID",
    description="Updates the Stripe Customer ID for the authenticated user",
    responses={
        200: {
            "description": "Stripe Customer ID updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Stripe Customer ID updated successfully"
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid Stripe Customer ID"}}}
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}}
        },
        409: {
            "description": "Stripe ID already in use",
            "content": {"application/json": {"example": {"detail": "Stripe ID already in use"}}}
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}}
        },
    }
)
async def update_stripe_customer_id(
    stripe_data: StripeCustomerIdUpdate,
    current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.update_stripe_customer_id(
            user_id=current_user["user_id"],
            stripe_customer_id=stripe_data.stripe_customer_id
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        return JSONResponse(content={
            "success": response.success,
            "message": response.message
        })
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.get(
    "/stripe-customer-id",
    response_model=StripeCustomerIdFetchResponse,
    summary="Fetch Stripe Customer ID",
    description="Retrieves the Stripe Customer ID for the authenticated user",
    responses={
        200: {
            "description": "Successfully retrieved Stripe Customer ID",
            "content": {
                "application/json": {
                    "example": {
                        "stripe_customer_id": "cus_xxx123xxx"
                    }
                }
            },
        },
        404: {
            "description": "User not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}}
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}}
        },
    }
)
async def fetch_stripe_customer_id(
    current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.fetch_stripe_customer_id(
            user_id=current_user["user_id"]
        )
        return JSONResponse(content={
            "stripe_customer_id": response.stripe_customer_id
        })
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@user_router.get("/get-user-details/{user_id}", response_model=UserBase)
async def get_user_details(user_id: str):
    try:
        validate_response = await user_service.validate_user(user_id)
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]
        return UserBase(
            email=user_details["email"],
            full_name=user_details["full_name"]
        )

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])