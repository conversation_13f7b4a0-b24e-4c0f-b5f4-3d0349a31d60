"""
Authentication Service gRPC Client

This module provides a gRPC client for communicating with the authentication service
for OAuth operations and credential management.
"""

import grpc
from typing import Optional, Dict, Any, List
import logging

from app.grpc_ import authentication_pb2, authentication_pb2_grpc
from app.core.config import settings
from app.core.oauth_providers import OAuthProvider

logger = logging.getLogger(__name__)


class AuthenticationServiceClient:
    """gRPC client for authentication service."""

    def __init__(self):
        """Initialize the authentication service client."""
        self.channel = None
        self.stub = None
        self._initialized = False

    def _ensure_connection(self):
        """Ensure gRPC connection is initialized."""
        if not self._initialized:
            self._initialize_connection()
            self._initialized = True

    def _initialize_connection(self):
        """Initialize gRPC connection to authentication service."""
        try:
            # Get authentication service configuration
            auth_service_host = getattr(settings, "AUTH_SERVICE_HOST", "localhost")
            auth_service_port = getattr(settings, "AUTH_SERVICE_PORT", 50054)

            # Create gRPC channel
            self.channel = grpc.insecure_channel(
                f"{auth_service_host}:{auth_service_port}",
                options=[
                    ("grpc.keepalive_time_ms", 30000),
                    ("grpc.keepalive_timeout_ms", 5000),
                    ("grpc.keepalive_permit_without_calls", True),
                    ("grpc.max_receive_message_length", 4 * 1024 * 1024),  # 4MB
                    ("grpc.max_send_message_length", 4 * 1024 * 1024),  # 4MB
                ],
            )

            # Create stub
            self.stub = authentication_pb2_grpc.AuthenticationServiceStub(self.channel)

            logger.info(
                f"Authentication service client initialized: {auth_service_host}:{auth_service_port}"
            )

        except Exception as e:
            logger.error(f"Failed to initialize authentication service client: {e}")
            raise

    def _convert_provider_to_grpc(self, provider: OAuthProvider) -> int:
        """Convert OAuthProvider enum to gRPC enum value."""
        logger.info(f"Converting provider: {provider} (type: {type(provider)})")

        provider_map = {
            OAuthProvider.GOOGLE: authentication_pb2.OAUTH_PROVIDER_GOOGLE,
            OAuthProvider.MICROSOFT: authentication_pb2.OAUTH_PROVIDER_MICROSOFT,
            OAuthProvider.GITHUB: authentication_pb2.OAUTH_PROVIDER_GITHUB,
            OAuthProvider.CUSTOM: authentication_pb2.OAUTH_PROVIDER_CUSTOM,
        }

        result = provider_map.get(provider, authentication_pb2.OAUTH_PROVIDER_GOOGLE)
        logger.info(f"Converted to gRPC enum: {result} (type: {type(result)})")
        return result

    def _handle_grpc_error(self, error: grpc.RpcError) -> Dict[str, Any]:
        """Handle gRPC errors and convert to standard response format."""
        logger.error(f"gRPC error: {error.code()} - {error.details()}")

        if error.code() == grpc.StatusCode.UNAVAILABLE:
            return {"success": False, "message": "Authentication service is currently unavailable"}
        elif error.code() == grpc.StatusCode.UNAUTHENTICATED:
            return {"success": False, "message": "Authentication failed"}
        elif error.code() == grpc.StatusCode.INVALID_ARGUMENT:
            return {"success": False, "message": f"Invalid request: {error.details()}"}
        else:
            return {"success": False, "message": f"Authentication service error: {error.details()}"}

    async def initiate_oauth(
        self,
        user_id: str,
        tool_name: str,
        provider: OAuthProvider,
        scopes: Optional[List[str]] = None,
        redirect_uri: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Initiate OAuth authorization flow."""
        try:
            self._ensure_connection()
            request = authentication_pb2.OAuthAuthorizeRequest(
                user_id=user_id,
                tool_name=tool_name,
                provider=self._convert_provider_to_grpc(provider),
                scopes=scopes or [],
                redirect_uri=redirect_uri or "",
            )

            response = self.stub.InitiateOAuth(request)

            return {
                "success": response.success,
                "message": response.message,
                "authorization_url": response.authorization_url,
                "state": response.state,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in initiate_oauth: {e}")
            return {"success": False, "message": "Failed to initiate OAuth flow"}

    async def handle_oauth_callback(
        self, code: Optional[str], state: str, error: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle OAuth callback."""
        try:
            request = authentication_pb2.OAuthCallbackRequest(
                code=code or "", state=state, error=error or ""
            )

            response = self.stub.HandleOAuthCallback(request)

            return {
                "success": response.success,
                "message": response.message,
                "user_id": response.user_id,
                "mcp_id": response.mcp_id,
                "tool_name": response.tool_name,
                "provider": response.provider,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in handle_oauth_callback: {e}")
            return {"success": False, "message": "Failed to handle OAuth callback"}

    async def get_oauth_credentials(
        self, user_id: str, mcp_id: str, tool_name: str, provider: OAuthProvider
    ) -> Dict[str, Any]:
        """Get OAuth credentials for a user."""
        try:
            self._ensure_connection()
            request = authentication_pb2.OAuthCredentialRequest(
                user_id=user_id,
                mcp_id=mcp_id,
                tool_name=tool_name,
                provider=self._convert_provider_to_grpc(provider),
            )

            response = self.stub.GetOAuthCredentials(request)

            if response.success:
                return {
                    "success": True,
                    "message": response.message,
                    "user_id": response.user_id,
                    "mcp_id": response.mcp_id,
                    "tool_name": response.tool_name,
                    "provider": response.provider,
                    "access_token": response.access_token,
                    "refresh_token": response.refresh_token,
                    "token_type": response.token_type,
                    "expires_in": response.expires_in,
                    "scope": response.scope,
                }
            else:
                return {"success": False, "message": response.message}

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_oauth_credentials: {e}")
            return {"success": False, "message": "Failed to retrieve OAuth credentials"}

    async def get_server_oauth_credentials(
        self,
        server_auth_key: str,
        user_id: str,
        mcp_id: str,
        tool_name: str,
        provider: OAuthProvider,
    ) -> Dict[str, Any]:
        """Get OAuth credentials using server authentication."""
        try:
            logger.info(
                f"Creating gRPC request for server OAuth credentials: user_id={user_id}, provider={provider}"
            )

            # Ensure connection is established
            self._ensure_connection()

            # Convert provider to gRPC enum
            grpc_provider = self._convert_provider_to_grpc(provider)
            logger.info(f"Converted provider {provider} to gRPC enum: {grpc_provider}")
            logger.info(f"grpc_provider type: {type(grpc_provider)}")

            # Debug all parameters
            logger.info(f"server_auth_key: {server_auth_key} (type: {type(server_auth_key)})")
            logger.info(f"user_id: {user_id} (type: {type(user_id)})")
            logger.info(f"mcp_id: {mcp_id} (type: {type(mcp_id)})")
            logger.info(f"tool_name: {tool_name} (type: {type(tool_name)})")

            try:
                request = authentication_pb2.ServerOAuthCredentialRequest(
                    server_auth_key=server_auth_key,
                    user_id=user_id,
                    mcp_id=mcp_id,
                    tool_name=tool_name,
                    provider=grpc_provider,
                )
                logger.info("Successfully created gRPC request")
            except Exception as req_error:
                logger.error(f"Error creating gRPC request: {req_error}")
                logger.error(f"Request error type: {type(req_error)}")
                raise

            logger.info(f"Making gRPC call to GetServerOAuthCredentials")
            response = self.stub.GetServerOAuthCredentials(request)
            logger.info(f"Received gRPC response: success={response.success}")

            if response.success:
                return {
                    "success": True,
                    "message": response.message,
                    "user_id": response.user_id,
                    "mcp_id": response.mcp_id,
                    "tool_name": response.tool_name,
                    "provider": response.provider,
                    "access_token": response.access_token,
                    "refresh_token": response.refresh_token,
                    "token_type": response.token_type,
                    "expires_in": response.expires_in,
                    "scope": response.scope,
                }
            else:
                return {"success": False, "message": response.message}

        except grpc.RpcError as e:
            logger.error(f"gRPC error in get_server_oauth_credentials: {e}")
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_server_oauth_credentials: {e}")
            logger.error(f"Error type: {type(e)}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"success": False, "message": "Failed to retrieve server OAuth credentials"}

    async def delete_oauth_credentials(
        self, user_id: str, mcp_id: str, tool_name: str, provider: OAuthProvider
    ) -> Dict[str, Any]:
        """Delete OAuth credentials."""
        try:
            request = authentication_pb2.DeleteOAuthCredentialRequest(
                user_id=user_id,
                mcp_id=mcp_id,
                tool_name=tool_name,
                provider=self._convert_provider_to_grpc(provider),
            )

            response = self.stub.DeleteOAuthCredentials(request)

            return {"success": response.success, "message": response.message}

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in delete_oauth_credentials: {e}")
            return {"success": False, "message": "Failed to delete OAuth credentials"}

    async def list_oauth_providers(self) -> Dict[str, Any]:
        """List available OAuth providers."""
        try:
            self._ensure_connection()
            request = authentication_pb2.OAuthProvidersListRequest()
            response = self.stub.ListOAuthProviders(request)

            providers = []
            for provider_info in response.providers:
                providers.append(
                    {
                        "provider": provider_info.name,
                        "display_name": provider_info.display_name,
                        "supported_tools": list(provider_info.supported_tools),
                        "is_configured": provider_info.is_configured,
                    }
                )

            return {
                "success": response.success,
                "message": response.message,
                "providers": providers,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in list_oauth_providers: {e}")
            return {"success": False, "message": "Failed to list OAuth providers", "providers": []}

    async def get_tool_scopes(self, tool_name: str, provider: OAuthProvider) -> Dict[str, Any]:
        """Get required scopes for a tool and provider."""
        try:
            request = authentication_pb2.OAuthToolScopesRequest(
                tool_name=tool_name, provider=self._convert_provider_to_grpc(provider)
            )

            response = self.stub.GetToolScopes(request)

            return {
                "success": response.success,
                "message": response.message,
                "tool_name": response.tool_name,
                "provider": response.provider,
                "scopes": list(response.scopes),
                "description": response.description,
            }

        except grpc.RpcError as e:
            return self._handle_grpc_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in get_tool_scopes: {e}")
            return {"success": False, "message": "Failed to get tool scopes", "scopes": []}

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on authentication service."""
        try:
            request = authentication_pb2.HealthCheckRequest()
            response = self.stub.HealthCheck(request)

            return {
                "healthy": response.healthy,
                "status": response.status,
                "version": response.version,
                "dependencies": dict(response.dependencies),
            }

        except grpc.RpcError as e:
            logger.error(f"Authentication service health check failed: {e}")
            return {
                "healthy": False,
                "status": "unhealthy",
                "version": "unknown",
                "dependencies": {},
            }
        except Exception as e:
            logger.error(f"Unexpected error in health_check: {e}")
            return {"healthy": False, "status": "error", "version": "unknown", "dependencies": {}}

    def close(self):
        """Close the gRPC connection."""
        if self.channel:
            self.channel.close()
            logger.info("Authentication service gRPC connection closed")


# Global authentication service client instance - lazy initialization
auth_service_client = None


def get_auth_service_client() -> AuthenticationServiceClient:
    """Get the global authentication service client instance."""
    global auth_service_client
    if auth_service_client is None:
        auth_service_client = AuthenticationServiceClient()
    return auth_service_client
